
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { complianceApi, ComplianceDocument, KYCProfile, ComplianceReport, WorkflowStatus } from '@/services/complianceApi';
import { toast } from 'sonner';

// Document submission hook
export const useSubmitDocument = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: complianceApi.submitDocument,
    onSuccess: (data) => {
      toast.success('Document submitted for compliance check');
      queryClient.invalidateQueries({ queryKey: ['documents'] });
    },
    onError: (error) => {
      toast.error('Failed to submit document: ' + error.message);
    }
  });
};

// KYC submission hook
export const useSubmitKYC = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: complianceApi.submitKYC,
    onSuccess: (data) => {
      toast.success('KYC processing initiated');
      queryClient.invalidateQueries({ queryKey: ['kyc'] });
    },
    onError: (error) => {
      toast.error('Failed to submit KYC: ' + error.message);
    }
  });
};

// Report generation hook
export const useGenerateReport = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: complianceApi.generateReport,
    onSuccess: (data) => {
      toast.success('Report generation started');
      queryClient.invalidateQueries({ queryKey: ['reports'] });
    },
    onError: (error) => {
      toast.error('Failed to generate report: ' + error.message);
    }
  });
};

// Workflow status hook
export const useWorkflowStatus = (workflowId: string | null, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['workflow', workflowId, 'status'],
    queryFn: () => workflowId ? complianceApi.getWorkflowStatus(workflowId) : null,
    enabled: enabled && !!workflowId,
    refetchInterval: (query) => {
      // Stop polling if workflow is completed or failed
      const workflowData = query.state.data;
      if (workflowData?.status === 'completed' || workflowData?.status === 'failed') {
        return false;
      }
      return 3000; // Poll every 3 seconds
    }
  });
};

// Workflow result hook
export const useWorkflowResult = (workflowId: string | null, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['workflow', workflowId, 'result'],
    queryFn: () => workflowId ? complianceApi.getWorkflowResult(workflowId) : null,
    enabled: enabled && !!workflowId
  });
};
