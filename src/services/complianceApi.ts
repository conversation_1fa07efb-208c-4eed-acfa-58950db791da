
// API service for DBOS compliance endpoints
const API_BASE_URL = 'http://localhost:3000';

// Types and Interfaces
export interface ComplianceDocument {
  id: string;
  content: string;
  documentType: 'contract' | 'policy' | 'procedure' | 'financial_report';
  uploadedAt: Date;
  status: 'pending' | 'processing' | 'compliant' | 'non_compliant' | 'requires_review';
}

export interface ComplianceRule {
  id: string;
  standard: 'SEC' | 'GLBA' | 'SOX' | 'GDPR' | 'CCPA';
  ruleType: 'data_protection' | 'financial_disclosure' | 'privacy' | 'security';
  description: string;
  pattern: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface KYCProfile {
  customerId: string;
  personalInfo: {
    name: string;
    dateOfBirth: string;
    ssn: string;
    address: string;
  };
  riskScore: number;
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  lastUpdated: Date;
}

export interface ComplianceViolation {
  documentId: string;
  ruleId: string;
  violationType: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendedAction: string;
  detectedAt: Date;
}

export interface ComplianceReport {
  id: string;
  reportType: 'monthly' | 'quarterly' | 'annual' | 'incident';
  generatedAt: Date;
  compliance_rate: number;
  violations: ComplianceViolation[];
  recommendations: string[];
}

export interface RegulatoryUpdate {
  id: string;
  standard: string;
  title: string;
  description: string;
  effectiveDate: Date;
  impact: 'low' | 'medium' | 'high';
  actionRequired: boolean;
}

export interface WorkflowStatus {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  currentStep: string;
  estimatedCompletion?: Date;
  result?: any;
}

// Dashboard Data Interfaces
export interface DashboardMetrics {
  complianceRate: number;
  activeViolations: number;
  pendingKYC: number;
  completedReports: number;
  regulatoryUpdates: number;
}

export interface ComplianceStandard {
  name: string;
  compliance: number;
  violations: number;
  lastCheck: string;
  status: 'compliant' | 'issues';
}

export interface RecentViolation {
  id: number;
  document: string;
  violation: string;
  severity: 'Critical' | 'High' | 'Medium' | 'Low';
  date: string;
  status: string;
}

export interface AIInsight {
  type: 'pattern' | 'improvement' | 'risk';
  title: string;
  description: string;
  color: 'blue' | 'green' | 'orange' | 'red';
}

export interface WorkflowPerformance {
  documentProcessing: string;
  kycCompletionRate: string;
  zeroDowntime: string;
  costSavings: string;
}

// API Functions
export const complianceApi = {
  // Submit document for compliance check
  async submitDocument(documentData: {
    content: string;
    documentType: ComplianceDocument['documentType'];
    fileName?: string;
  }): Promise<{ workflowId: string; document: ComplianceDocument }> {
    const response = await fetch(`${API_BASE_URL}/api/compliance/document`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(documentData),
    });
    
    if (!response.ok) {
      throw new Error('Failed to submit document for compliance check');
    }
    
    return response.json();
  },

  // Submit customer for KYC processing
  async submitKYC(customerData: {
    personalInfo: KYCProfile['personalInfo'];
    additionalInfo?: any;
  }): Promise<{ workflowId: string; profile: KYCProfile }> {
    const response = await fetch(`${API_BASE_URL}/api/kyc/customer`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(customerData),
    });
    
    if (!response.ok) {
      throw new Error('Failed to submit customer for KYC processing');
    }
    
    return response.json();
  },

  // Generate compliance reports
  async generateReport(reportConfig: {
    reportType: ComplianceReport['reportType'];
    dateRange?: {
      startDate: string;
      endDate: string;
    };
    standards?: string[];
  }): Promise<{ workflowId: string; report: ComplianceReport }> {
    const response = await fetch(`${API_BASE_URL}/api/reports/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(reportConfig),
    });
    
    if (!response.ok) {
      throw new Error('Failed to generate compliance report');
    }
    
    return response.json();
  },

  // Get workflow progress
  async getWorkflowStatus(workflowId: string): Promise<WorkflowStatus> {
    const response = await fetch(`${API_BASE_URL}/api/workflow/${workflowId}/status`);
    
    if (!response.ok) {
      throw new Error('Failed to get workflow status');
    }
    
    return response.json();
  },

  // Get workflow results
  async getWorkflowResult(workflowId: string): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/api/workflow/${workflowId}/result`);

    if (!response.ok) {
      throw new Error('Failed to get workflow result');
    }

    return response.json();
  },

  // Dashboard Data API Functions
  async getDashboardMetrics(): Promise<DashboardMetrics> {
    const response = await fetch(`${API_BASE_URL}/api/dashboard/metrics`);

    if (!response.ok) {
      throw new Error('Failed to fetch dashboard metrics');
    }

    return response.json();
  },

  async getComplianceStandards(): Promise<ComplianceStandard[]> {
    const response = await fetch(`${API_BASE_URL}/api/dashboard/compliance-standards`);

    if (!response.ok) {
      throw new Error('Failed to fetch compliance standards');
    }

    return response.json();
  },

  async getRecentViolations(): Promise<RecentViolation[]> {
    const response = await fetch(`${API_BASE_URL}/api/dashboard/recent-violations`);

    if (!response.ok) {
      throw new Error('Failed to fetch recent violations');
    }

    return response.json();
  },

  async getAIInsights(): Promise<AIInsight[]> {
    const response = await fetch(`${API_BASE_URL}/api/dashboard/ai-insights`);

    if (!response.ok) {
      throw new Error('Failed to fetch AI insights');
    }

    return response.json();
  },

  async getWorkflowPerformance(): Promise<WorkflowPerformance> {
    const response = await fetch(`${API_BASE_URL}/api/dashboard/workflow-performance`);

    if (!response.ok) {
      throw new Error('Failed to fetch workflow performance');
    }

    return response.json();
  },

  // Document API Functions
  async getRecentDocuments(): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/api/documents/recent`);

    if (!response.ok) {
      throw new Error('Failed to fetch recent documents');
    }

    return response.json();
  },

  async getDocumentStats(): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/api/documents/stats`);

    if (!response.ok) {
      throw new Error('Failed to fetch document stats');
    }

    return response.json();
  },

  // Regulatory API Functions
  async getRegulatoryUpdates(): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/api/regulatory/updates`);

    if (!response.ok) {
      throw new Error('Failed to fetch regulatory updates');
    }

    return response.json();
  },

  async getRegulatoryStats(): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/api/regulatory/stats`);

    if (!response.ok) {
      throw new Error('Failed to fetch regulatory stats');
    }

    return response.json();
  },

  async getMonitoredSources(): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/api/regulatory/sources`);

    if (!response.ok) {
      throw new Error('Failed to fetch monitored sources');
    }

    return response.json();
  },

  // KYC API Functions
  async getKYCQueue(): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/api/kyc/queue`);

    if (!response.ok) {
      throw new Error('Failed to fetch KYC queue');
    }

    return response.json();
  },

  async getKYCStats(): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/api/kyc/stats`);

    if (!response.ok) {
      throw new Error('Failed to fetch KYC stats');
    }

    return response.json();
  },

  // Reports API Functions
  async getRecentReports(): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/api/reports/recent`);

    if (!response.ok) {
      throw new Error('Failed to fetch recent reports');
    }

    return response.json();
  },

  async getReportStats(): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/api/reports/stats`);

    if (!response.ok) {
      throw new Error('Failed to fetch report stats');
    }

    return response.json();
  },

  // Workflows API Functions
  async getActiveWorkflows(): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/api/workflows/active`);

    if (!response.ok) {
      throw new Error('Failed to fetch active workflows');
    }

    return response.json();
  },

  async getWorkflowStats(): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/api/workflows/stats`);

    if (!response.ok) {
      throw new Error('Failed to fetch workflow stats');
    }

    return response.json();
  },

  async getWorkflowMetrics(): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/api/workflows/metrics`);

    if (!response.ok) {
      throw new Error('Failed to fetch workflow metrics');
    }

    return response.json();
  }
};
