
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Users, Clock, Shield, AlertTriangle, CheckCircle, Search } from 'lucide-react';
import { toast } from 'sonner';
import { complianceApi } from '@/services/complianceApi';

const KYCCenter = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [kycQueue, setKycQueue] = useState<any[]>([]);
  const [kycStats, setKycStats] = useState<any>({
    totalProcessed: 0,
    averageTime: '0 days',
    automationRate: 0,
    pendingReview: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [queue, stats] = await Promise.all([
          complianceApi.getKYCQueue(),
          complianceApi.getKYCStats()
        ]);

        setKycQueue(queue);
        setKycStats(stats);
      } catch (error) {
        console.error('Failed to fetch KYC data:', error);
        // Keep default values on error
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'Low': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'High': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Manual Review Required': return 'bg-red-100 text-red-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const handleStartKYC = () => {
    toast.success('New KYC process initiated. Customer will receive verification instructions.');
  };

  const filteredQueue = kycQueue.filter(item =>
    item.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.id.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* KYC Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Processed</p>
                <p className="text-2xl font-bold text-blue-600">{kycStats.totalProcessed}</p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">This quarter</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Average Time</p>
                <p className="text-2xl font-bold text-green-600">{kycStats.averageTime}</p>
              </div>
              <Clock className="w-8 h-8 text-green-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">90% faster processing</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Automation Rate</p>
                <p className="text-2xl font-bold text-purple-600">{kycStats.automationRate}%</p>
              </div>
              <Shield className="w-8 h-8 text-purple-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">Automated verification</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Review</p>
                <p className="text-2xl font-bold text-orange-600">{kycStats.pendingReview}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">Manual intervention needed</p>
          </CardContent>
        </Card>
      </div>

      {/* New KYC Process */}
      <Card>
        <CardHeader>
          <CardTitle>Initiate New KYC Process</CardTitle>
          <CardDescription>
            Start automated customer verification with AI-powered identity validation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="customerEmail">Customer Email</Label>
              <Input id="customerEmail" placeholder="<EMAIL>" />
            </div>
            <div>
              <Label htmlFor="customerType">Customer Type</Label>
              <select className="w-full p-2 border rounded-md">
                <option>Individual</option>
                <option>Corporate</option>
                <option>Non-Profit</option>
              </select>
            </div>
            <div className="flex items-end">
              <Button onClick={handleStartKYC} className="w-full">
                Start KYC Process
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* KYC Queue */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>KYC Processing Queue</CardTitle>
              <CardDescription>
                Real-time status of customer verification workflows
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search customers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 w-64"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredQueue.map((item) => (
              <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="font-medium">{item.customerName}</h4>
                    <Badge variant="outline">{item.id}</Badge>
                    <Badge className={getRiskColor(item.riskScore)}>
                      {item.riskScore} Risk
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                    <span>Submitted: {item.submissionDate}</span>
                    <span>•</span>
                    <span>Time remaining: {item.timeRemaining}</span>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="flex-1">
                      <div className="flex items-center justify-between text-xs mb-1">
                        <span>Progress: {item.completedSteps}/{item.totalSteps}</span>
                        <span>{Math.round((item.completedSteps / item.totalSteps) * 100)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${(item.completedSteps / item.totalSteps) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    {item.flags.length > 0 && (
                      <div className="flex space-x-1">
                        {item.flags.map((flag: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs bg-orange-50 text-orange-700">
                            {flag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Badge className={getStatusColor(item.status)}>
                    {item.status === 'Completed' && <CheckCircle className="w-3 h-3 mr-1" />}
                    {item.status === 'Manual Review Required' && <AlertTriangle className="w-3 h-3 mr-1" />}
                    {item.status}
                  </Badge>
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* KYC Automation Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Automated Verification Steps</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium">Identity Document Verification</p>
                  <p className="text-sm text-gray-600">AI-powered document authenticity checks</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium">Sanctions Screening</p>
                  <p className="text-sm text-gray-600">Real-time OFAC and global sanctions checks</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium">Risk Assessment</p>
                  <p className="text-sm text-gray-600">Multi-factor risk scoring and analysis</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium">PEP & Adverse Media</p>
                  <p className="text-sm text-gray-600">Politically exposed persons screening</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>DBOS Workflow Benefits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-1">Fault Tolerance</h4>
                <p className="text-sm text-blue-700">Workflows resume automatically from interruption points</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-1">Parallel Processing</h4>
                <p className="text-sm text-green-700">Multiple verification steps run concurrently</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-1">Audit Trail</h4>
                <p className="text-sm text-purple-700">Complete workflow history for compliance</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-1">Rate Limiting</h4>
                <p className="text-sm text-orange-700">Managed API calls prevent service overload</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default KYCCenter;
