import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Upload, File, CheckCircle, AlertTriangle, Clock, Eye } from 'lucide-react';
import { toast } from 'sonner';
import { useSubmitDocument, useWorkflowStatus } from '@/hooks/useComplianceApi';
import { ComplianceDocument, complianceApi } from '@/services/complianceApi';

const DocumentUpload = () => {
  const [dragActive, setDragActive] = useState(false);
  const [currentWorkflowId, setCurrentWorkflowId] = useState<string | null>(null);
  const [recentDocuments, setRecentDocuments] = useState<any[]>([]);
  const [documentStats, setDocumentStats] = useState<any>({
    documentsScanned: 0,
    violationsDetected: 0,
    avgProcessingTime: '0s',
    violationRate: 0
  });
  const [loading, setLoading] = useState(true);

  const submitDocumentMutation = useSubmitDocument();
  const { data: workflowStatus } = useWorkflowStatus(currentWorkflowId);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [documents, stats] = await Promise.all([
          complianceApi.getRecentDocuments(),
          complianceApi.getDocumentStats()
        ]);

        setRecentDocuments(documents);
        setDocumentStats(stats);
      } catch (error) {
        console.error('Failed to fetch document data:', error);
        // Keep default values on error
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileUpload(files[0]);
    }
  };

  const handleFileUpload = async (file: File) => {
    try {
      console.log('Uploading file:', file.name);
      
      // Convert file to base64 content
      const fileContent = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsText(file);
      });

      // Determine document type based on file name
      const documentType: ComplianceDocument['documentType'] = 
        file.name.toLowerCase().includes('financial') ? 'financial_report' :
        file.name.toLowerCase().includes('contract') ? 'contract' :
        file.name.toLowerCase().includes('policy') ? 'policy' : 'procedure';

      const result = await submitDocumentMutation.mutateAsync({
        content: fileContent,
        documentType,
        fileName: file.name
      });

      setCurrentWorkflowId(result.workflowId);
      console.log('Document submitted, workflow ID:', result.workflowId);
      
    } catch (error) {
      console.error('File upload failed:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Compliant':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'Violation Detected':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'Processing':
        return <Clock className="w-4 h-4 text-orange-500" />;
      default:
        return <File className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Compliant':
        return 'bg-green-100 text-green-800';
      case 'Violation Detected':
        return 'bg-red-100 text-red-800';
      case 'Processing':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Upload className="w-5 h-5 mr-2" />
            Document Upload & AI Compliance Scanning
          </CardTitle>
          <CardDescription>
            Upload documents for automated compliance checking against SEC, GLBA, SOX, and FINRA standards
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">
              {dragActive ? 'Drop files here' : 'Upload Documents'}
            </h3>
            <p className="text-gray-600 mb-4">
              Drag and drop your files here or click to browse
            </p>
            <Button 
              onClick={() => document.getElementById('fileInput')?.click()}
              disabled={submitDocumentMutation.isPending}
            >
              {submitDocumentMutation.isPending ? 'Uploading...' : 'Choose Files'}
            </Button>
            <input
              id="fileInput"
              type="file"
              className="hidden"
              multiple
              accept=".pdf,.doc,.docx,.txt"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleFileUpload(file);
              }}
            />
            <p className="text-xs text-gray-500 mt-2">
              Supported formats: PDF, DOC, DOCX, TXT (Max 50MB)
            </p>
          </div>

          {/* Workflow Status */}
          {workflowStatus && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-blue-900">
                  {workflowStatus.currentStep}
                </span>
                <span className="text-sm text-blue-700">{workflowStatus.progress}%</span>
              </div>
              <Progress value={workflowStatus.progress} className="mb-2" />
              <p className="text-xs text-blue-600">
                Status: {workflowStatus.status}
                {workflowStatus.estimatedCompletion && (
                  <span> • ETA: {new Date(workflowStatus.estimatedCompletion).toLocaleTimeString()}</span>
                )}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Documents */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Document Scans</CardTitle>
          <CardDescription>
            AI-powered compliance analysis results
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="flex items-center space-x-4 p-4 border rounded-lg">
                    <div className="w-8 h-8 bg-gray-200 rounded"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2 mb-1"></div>
                      <div className="flex space-x-2">
                        <div className="h-3 bg-gray-200 rounded w-12"></div>
                        <div className="h-3 bg-gray-200 rounded w-12"></div>
                      </div>
                    </div>
                    <div className="w-20 h-8 bg-gray-200 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {recentDocuments.map((doc) => (
                <div key={doc.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <File className="w-8 h-8 text-blue-500" />
                    <div>
                      <h4 className="font-medium">{doc.name}</h4>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-sm text-gray-500">{doc.size}</span>
                        <span className="text-sm text-gray-400">•</span>
                        <span className="text-sm text-gray-500">{doc.uploadDate}</span>
                      </div>
                      <div className="flex items-center space-x-2 mt-1">
                        {doc.complianceChecks.map((check: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {check}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(doc.status)}
                        <Badge className={getStatusColor(doc.status)}>
                          {doc.status}
                        </Badge>
                      </div>
                      {doc.violations !== null && doc.violations > 0 && (
                        <p className="text-xs text-red-600 mt-1">
                          {doc.violations} violation{doc.violations > 1 ? 's' : ''} found
                        </p>
                      )}
                    </div>
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            {loading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Documents Scanned</p>
                    <p className="text-2xl font-bold text-blue-600">{documentStats.documentsScanned}</p>
                  </div>
                  <File className="w-8 h-8 text-blue-500" />
                </div>
                <p className="text-xs text-gray-500 mt-1">This month</p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            {loading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Violations Detected</p>
                    <p className="text-2xl font-bold text-red-600">{documentStats.violationsDetected}</p>
                  </div>
                  <AlertTriangle className="w-8 h-8 text-red-500" />
                </div>
                <p className="text-xs text-gray-500 mt-1">{documentStats.violationRate}% violation rate</p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            {loading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Processing Time</p>
                    <p className="text-2xl font-bold text-green-600">{documentStats.avgProcessingTime}</p>
                  </div>
                  <Clock className="w-8 h-8 text-green-500" />
                </div>
                <p className="text-xs text-gray-500 mt-1">90% faster than manual</p>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DocumentUpload;
