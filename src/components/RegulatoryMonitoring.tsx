
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Activity, AlertTriangle, CheckCircle, Clock, ExternalLink, Bell } from 'lucide-react';
import { complianceApi } from '@/services/complianceApi';

const RegulatoryMonitoring = () => {
  const [regulatoryUpdates, setRegulatoryUpdates] = useState<any[]>([]);
  const [monitoringStats, setMonitoringStats] = useState<any>({
    totalUpdates: 0,
    pendingReview: 0,
    actionRequired: 0,
    avgResponseTime: '0 days'
  });
  const [monitoredSources, setMonitoredSources] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [updates, stats, sources] = await Promise.all([
          complianceApi.getRegulatoryUpdates(),
          complianceApi.getRegulatoryStats(),
          complianceApi.getMonitoredSources()
        ]);

        setRegulatoryUpdates(updates);
        setMonitoringStats(stats);
        setMonitoredSources(sources);
      } catch (error) {
        console.error('Failed to fetch regulatory monitoring data:', error);
        // Keep default values on error
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'High': return 'bg-red-100 text-red-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Action Required': return 'bg-red-100 text-red-800';
      case 'Under Review': return 'bg-orange-100 text-orange-800';
      case 'Monitoring': return 'bg-blue-100 text-blue-800';
      case 'Implemented': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Action Required': return <AlertTriangle className="w-4 h-4" />;
      case 'Under Review': return <Clock className="w-4 h-4" />;
      case 'Monitoring': return <Activity className="w-4 h-4" />;
      case 'Implemented': return <CheckCircle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Monitoring Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            {loading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Updates</p>
                    <p className="text-2xl font-bold text-blue-600">{monitoringStats.totalUpdates}</p>
                  </div>
                  <Activity className="w-8 h-8 text-blue-500" />
                </div>
                <p className="text-xs text-gray-500 mt-1">This quarter</p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            {loading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending Review</p>
                    <p className="text-2xl font-bold text-orange-600">{monitoringStats.pendingReview}</p>
                  </div>
                  <Clock className="w-8 h-8 text-orange-500" />
                </div>
                <p className="text-xs text-gray-500 mt-1">Needs attention</p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            {loading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Action Required</p>
                    <p className="text-2xl font-bold text-red-600">{monitoringStats.actionRequired}</p>
                  </div>
                  <AlertTriangle className="w-8 h-8 text-red-500" />
                </div>
                <p className="text-xs text-gray-500 mt-1">Immediate action needed</p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            {loading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Response</p>
                    <p className="text-2xl font-bold text-green-600">{monitoringStats.avgResponseTime}</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-500" />
                </div>
                <p className="text-xs text-gray-500 mt-1">Implementation time</p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Regulatory Updates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bell className="w-5 h-5 mr-2" />
            Recent Regulatory Updates
          </CardTitle>
          <CardDescription>
            Automated monitoring of regulatory changes with impact analysis
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {regulatoryUpdates.map((update) => (
              <div key={update.id} className="p-4 border rounded-lg">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge variant="outline">{update.source}</Badge>
                      <Badge className={getImpactColor(update.impact)}>
                        {update.impact} Impact
                      </Badge>
                      <Badge className={getStatusColor(update.status)}>
                        {getStatusIcon(update.status)}
                        <span className="ml-1">{update.status}</span>
                      </Badge>
                    </div>
                    <h4 className="font-semibold mb-1">{update.title}</h4>
                    <p className="text-sm text-gray-600 mb-2">{update.description}</p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Published: {update.publishDate}</span>
                      <span>•</span>
                      <span>Effective: {update.effectiveDate}</span>
                      <span>•</span>
                      <span>{update.affectedDocuments} documents affected</span>
                    </div>
                  </div>
                  <div className="flex space-x-2 ml-4">
                    <Button variant="outline" size="sm">
                      <ExternalLink className="w-4 h-4 mr-1" />
                      View Source
                    </Button>
                    <Button variant="default" size="sm">
                      Analyze Impact
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Monitoring Sources */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Monitored Sources</CardTitle>
            <CardDescription>
              Real-time tracking of regulatory bodies and standards
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {monitoredSources.map((source, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">{source.name}</p>
                      <p className="text-xs text-gray-500">Last check: {source.lastCheck}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">{source.updates} updates</p>
                    <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                      {source.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>AI Analysis Features</CardTitle>
            <CardDescription>
              Automated regulatory change impact assessment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-1">Impact Prediction</h4>
                <p className="text-sm text-blue-700">AI predicts which documents need updates</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-1">Priority Ranking</h4>
                <p className="text-sm text-green-700">Automatic prioritization based on compliance risk</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-1">Timeline Analysis</h4>
                <p className="text-sm text-purple-700">Implementation timeline recommendations</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-1">Change Tracking</h4>
                <p className="text-sm text-orange-700">Complete audit trail of regulatory responses</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default RegulatoryMonitoring;
